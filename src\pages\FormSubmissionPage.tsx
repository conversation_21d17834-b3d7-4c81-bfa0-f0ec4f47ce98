import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { FormProvider } from "react-hook-form";
import { useFormSubmission } from "@/hooks/useFormSubmission";
import { evaluateConditionalRendering } from "@/lib/utils/component-utils";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle, Loader2, Save } from "lucide-react";
import { Loading } from "@/components/ui/loading";
import { useToast } from "@/hooks/use-toast";
import RenderComponent from "@/components/form-builder/form-components/RenderComponent";
import StepProgress from "@/components/form-builder/form-components/StepProgress";
import StepNavigation from "@/components/form-builder/form-components/StepNavigation";

// Form Status Message Component
interface FormStatusMessageProps {
  isSubmitted: boolean;
  isValid: boolean;
  message: string;
}

const FormStatusMessage = ({
  isSubmitted,
  isValid,
  message,
}: FormStatusMessageProps) => {
  if (!isSubmitted || !message) return null;

  return (
    <Alert variant={isValid ? "default" : "destructive"}>
      <AlertCircle className="h-4 w-4" />
      <AlertDescription>{message}</AlertDescription>
    </Alert>
  );
};

export default function FormSubmissionPage() {
  const { formId, projectRef } = useParams<{
    formId?: string;
    projectRef?: string;
  }>();
  const { toast } = useToast();


  // Use form submission hook
  const {
    form,
    methods,
    isLoading,
    isSaving,
    isSubmitting,
    formStatus,
    steps,
    isMultiStep,
    currentStep,
    currentStepData,
    nextStep,
    prevStep,
    saveProgress,
    submitForm,
  } = useFormSubmission({
    formId: formId ?? "",
    projectRef

  });

  // Extract methods from react-hook-form
  const {
    handleSubmit,
    control,
    formState: { errors },
    setValue,
    watch,
  } = methods;

  // Handle form submission
  const onSubmit = async () => {
    const success = await submitForm();
    if (success) {
      toast({
        title: "Form submitted successfully",
        description: "Thank you for your submission!",
        variant: "success",
      });
    } else {
      toast({
        title: "Submission failed",
        description: "Please check the form for errors and try again.",
        variant: "destructive",
      });
    }
  };

  // Handle save progress with better user feedback
  const handleSaveProgress = async () => {
    // Show saving toast immediately for better user feedback
    toast({
      title: "Saving progress...",
      description: "Please wait while we save your progress.",
      variant: "default",
    });

    try {
      const success = await saveProgress();

      if (success) {
        toast({
          title: "Progress saved",
          description: "Your progress has been saved. You can continue later.",
          variant: "success",
        });
      } else {
        toast({
          title: "Save failed",
          description: "Failed to save your progress. Please try again.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error in handleSaveProgress:", error);
      toast({
        title: "Save failed",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Handle validation errors
  const onError = (errors: any) => {
    toast({
      title: "Validation errors",
      description: "Please fix the errors in the form before submitting.",
      variant: "destructive",
    });

    // Scroll to the first error
    const firstErrorKey = Object.keys(errors)[0];
    const firstErrorElement = document.getElementsByName(firstErrorKey)[0];
    if (firstErrorElement) {
      firstErrorElement.scrollIntoView({ behavior: "smooth", block: "center" });
    }
  };

  // Show loading state
  if (isLoading) {
    return <Loading message="Loading form..." />;
  }

  // Handle case when form is not found
  if (!form) {
    return (
      <div className="flex flex-col items-center justify-center py-10 text-center">
        <p className="text-muted-foreground">
          Form not found or you don't have permission to access it.
        </p>
        <Button asChild className="mt-4">
          <Link to="/applications">Back to Applications</Link>
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h1 className="text-3xl font-bold tracking-tight">{form.name}</h1>
      </div>

      <Card className="mx-auto max-w-3xl">
        <CardHeader>
          <CardTitle>{form.name}</CardTitle>
          {form.description && (
            <CardDescription>{form.description}</CardDescription>
          )}

          {isMultiStep && (
            <StepProgress
              currentStep={currentStep}
              totalSteps={steps.length}
              stepLabel={currentStepData.label}
              stepDescription={currentStepData.description}
            />
          )}
        </CardHeader>

        <CardContent>
          <FormProvider {...methods}>
            <form
              onSubmit={handleSubmit(onSubmit, onError)}
              className="space-y-6"
            >
              <FormStatusMessage
                isSubmitted={formStatus.isSubmitted}
                isValid={formStatus.isValid}
                message={formStatus.message}
              />

              {currentStepData.components.map((component) => {
                // Check if this component should be rendered based on conditional rules
                const shouldRender = evaluateConditionalRendering(
                  component,
                  watch
                );

                if (!shouldRender) {
                  return null; // Skip rendering this component
                }

                return (
                  <RenderComponent
                    key={component.id}
                    component={component}
                    register={methods.register}
                    control={control}
                    errors={errors}
                    setValue={setValue}
                    watch={watch}
                    validationRules={{}}
                    allComponents={form.components}
                    mode="submission"
                  />
                );
              })}

              {/* Only show step navigation if the form is multi-step */}
              {isMultiStep ? (
                <div className="flex justify-between mt-6">
                  {/* Save progress button */}
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleSaveProgress}
                    disabled={isSaving}
                  >
                    {isSaving ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Save Progress
                      </>
                    )}
                  </Button>

                  <StepNavigation
                    currentStep={currentStep}
                    totalSteps={steps.length}
                    onNext={nextStep}
                    onPrevious={prevStep}
                    isMultiStep={isMultiStep}
                    onSubmit={handleSubmit(onSubmit, onError)}
                    isLoading={isSaving}
                  />
                </div>
              ) : (
                <div className="mt-6 space-y-4">
                  <Button
                    type="button"
                    onClick={handleSaveProgress}
                    variant="outline"
                    className="w-full"
                    disabled={isSaving}
                  >
                    {isSaving ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="mr-2 h-4 w-4" />
                        Save Progress
                      </>
                    )}
                  </Button>

                  <StepNavigation
                    currentStep={0}
                    totalSteps={1}
                    onNext={() => { }}
                    onPrevious={() => { }}
                    isMultiStep={false}
                    onSubmit={handleSubmit(onSubmit, onError)}
                    isLoading={isSubmitting}
                  />
                </div>
              )}
            </form>
          </FormProvider>
        </CardContent>

        <CardFooter className="flex justify-center text-sm text-muted-foreground">
          <div>
            You can save your progress and return to complete this form later.
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
